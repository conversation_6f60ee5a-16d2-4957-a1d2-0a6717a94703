import 'dotenv/config';
import app from './app.js';
import { logInfo, logSuccess, logError } from './utils/logger.js';

const port = process.env.PORT || 3001;

/**
 * Gracefully shutdown the server
 * @param {Object} options Options for shutdown
 * @param {string} [options.message] Optional message to log
 * @param {Object} options.server The Express server instance
 * @param {Error} [options.error] Optional error to log
 */
function shutdown({ message = '', server, error }) {
  if (error) {
    if (message) logError('Message:', message);
    logError('Error:', error.name, error.message);
    logError(error.stack);
    logError('💥 Shutting down...');
  } else {
    if (message) logInfo(message);
    logInfo('👋 Shutting down gracefully...');
  }

  server.close(() => {
    logInfo('💥 Process terminated');
    process.exit(error ? 1 : 0);
  });
}

// Start the server
logInfo('Starting Stream Program API...');

const server = app.listen(port, (error) => {
  if (error) {
    logError('Failed to start server:', error);
    process.exit(1);
  }

  logSuccess(`🚀 Stream Program API running on port ${port}...`);
  logInfo(`Health check: http://localhost:${port}/health`);
  logInfo(`Random program: http://localhost:${port}/stream-program`);
});

// Error handling
process.on('uncaughtException', (error) => {
  shutdown({ message: 'UNCAUGHT EXCEPTION!', error, server });
});

process.on('unhandledRejection', (error) => {
  shutdown({ message: 'UNHANDLED REJECTION!', error, server });
});

// Graceful shutdown signals
process.on('SIGTERM', () =>
  shutdown({ message: 'SIGTERM RECEIVED.', server })
);

process.on('SIGINT', () =>
  shutdown({ message: 'SIGINT RECEIVED.', server })
);
