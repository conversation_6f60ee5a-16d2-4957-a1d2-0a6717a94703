# Stream Program API

A simple REST API that provides random streaming programs for Hope Media. The API returns a random streaming program with title, description, and image URL from a curated collection of faith-based content.

## Installation

### Prerequisites
- Node.js (version 14 or higher)
- npm (comes with Node.js)

### Setup

1. Clone or navigate to the project directory:
   ```bash
   cd stream-program-api
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the root directory (optional):
   ```bash
   PORT=3001
   ```

## Running the Application

### Development Mode (with auto-reload)
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

The server will start on port 3001 by default (or the port specified in your `.env` file).

You'll see output like:
```
🚀 Stream Program API running on port 3001...
Health check: http://localhost:3001/health
Random program: http://localhost:3001/stream-program
```

## API Endpoints

### Get Random Stream Program
Returns a random streaming program from the collection.

- **URL**: `/stream-program`
- **Method**: `GET`
- **Response**: JSON object containing program details

#### Response Format
```json
{
  "title": "Faith in Action",
  "description": "Real-life stories of people living out their faith every day.",
  "imageUrl": "https://hopemedia.example/img/faith-in-action.jpg"
}
```

#### Example Usage

**cURL:**
```bash
curl http://localhost:3001/stream-program
```

**JavaScript (fetch):**
```javascript
fetch('http://localhost:3001/stream-program')
  .then(response => response.json())
  .then(program => console.log(program));
```

**Response Example:**
```json
{
  "title": "Walking with Christ",
  "description": "A journey through scripture that deepens your relationship with Jesus.",
  "imageUrl": "https://hopemedia.example/img/walking-with-christ.jpg"
}
```

### Health Check
Check if the API is running properly.

- **URL**: `/health`
- **Method**: `GET`
- **Response**: 
```json
{
  "status": "ok",
  "timestamp": "2024-01-20T10:30:00.000Z"
}
```

## Available Programs

The API includes 10 different faith-based streaming programs:

1. **Faith in Action** - Real-life stories of people living out their faith every day
2. **Walking with Christ** - A journey through scripture that deepens your relationship with Jesus
3. **Hope for Tomorrow** - Inspiring messages of hope and renewal for difficult times
4. **Grace Unveiled** - Discovering the transformative power of God's grace in our lives
5. **Miracles Among Us** - Testimonies of modern-day miracles and divine interventions
6. **Sacred Moments** - Finding the sacred in everyday moments through prayer and reflection
7. **Building Faith Together** - Stories of communities coming together to strengthen their faith
8. **The Light Within** - Exploring how God's light shines through us to illuminate the world
9. **Blessed Beginnings** - Celebrating new chapters of life through God's blessings and guidance
10. **Eternal Promises** - Understanding God's eternal promises and their meaning for our lives

## CORS Support

The API includes CORS headers to allow cross-origin requests from web browsers, making it suitable for frontend applications.

## Error Handling

- **404**: Returned for undefined routes
- **500**: Returned for server errors
- All errors include a JSON response with `status` and `message` fields

## Logging

The application includes comprehensive logging:
- Request logging for all incoming requests
- Startup and shutdown logging
- Error logging with stack traces
- Colored console output for better visibility

## Scripts

- `npm start` - Start the server in production mode
- `npm run dev` - Start the server in development mode with nodemon (auto-restart on changes)
- `npm test` - Run tests (currently not implemented)

## Project Structure

```
stream-program-api/
├── app.js                     # Express app configuration
├── server.js                  # Server startup and process management
├── package.json               # Dependencies and scripts
├── routes/
│   └── streamProgramRoutes.js # API routes
├── data/
│   └── streamPrograms.js      # Program data
└── utils/
    └── logger.js              # Logging utilities
```
